<template>
  <div
    class="min-h-screen bg-gradient-to-br from-phantom-dark via-phantom-dark-blue to-black font-sans relative overflow-hidden"
  >
    <!-- Phantom Background Effects -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- Grid Pattern -->
      <div class="absolute inset-0 bg-grid-phantom opacity-20" />

      <!-- Floating Particles -->
      <div
        v-for="i in 30"
        :key="i"
        class="absolute rounded-full"
        :class="[
          i % 3 === 0 ? 'bg-phantom-blue/20 animate-float-slow' : '',
          i % 3 === 1 ? 'bg-phantom-indigo/20 animate-float-slow-reverse' : '',
          i % 3 === 2 ? 'bg-phantom-purple/20 animate-pulse-slow' : '',
        ]"
        :style="{
          width: `${Math.random() * 8 + 3}px`,
          height: `${Math.random() * 8 + 3}px`,
          top: `${Math.random() * 100}%`,
          left: `${Math.random() * 100}%`,
          animationDelay: `${Math.random() * 5}s`,
        }"
      />

      <!-- Radial Vignette -->
      <div class="absolute inset-0 bg-radial-vignette" />
    </div>

    <!-- Main Content -->
    <div
      class="relative z-10 min-h-screen flex items-center justify-center -mt-24"
    >
      <div class="w-full max-w-6xl">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2 class="text-xl font-bold text-white">
            {{ assessmentName }}
          </h2>
        </div>

        <!-- User Details Form -->
        <div v-if="!quizStarted" class="card-phantom p-10 pb-16 shadow-glow-md">
          <h2
            class="text-2xl font-bold text-white mb-6 text-center bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
          >
            Enter Your Details
          </h2>

          <!-- Fullscreen Mode Warning -->
          <div
            class="mb-6 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg"
          >
            <div class="flex items-start">
              <svg
                class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clip-rule="evenodd"
                />
              </svg>
              <div>
                <h3 class="text-yellow-400 font-semibold mb-2">
                  Important: Fullscreen Quiz Mode
                </h3>
                <ul class="text-yellow-300 text-sm space-y-1">
                  <li>
                    • The quiz will run in fullscreen mode to prevent
                    distractions
                  </li>
                  <li>
                    • Attempting to exit, switch tabs, or use shortcuts will be
                    monitored
                  </li>
                  <li>
                    • Any attempt to exit will immediately submit your quiz
                  </li>
                  <li>
                    • Ensure you're ready to complete the entire quiz before
                    starting
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <form class="space-y-6" @submit.prevent="startQuiz">
            <!-- Session Code Field -->
            <div>
              <label
                for="sessionCode"
                class="block text-sm font-medium text-white/80 mb-2"
              >
                Session Code (Optional)
              </label>
              <input
                id="sessionCode"
                v-model="existingSessionCode"
                name="sessionCode"
                type="text"
                maxlength="6"
                pattern="[0-9]{6}"
                autocomplete="off"
                class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 font-mono text-center text-lg tracking-widest placeholder-white/40"
                placeholder="123456"
                @input="onSessionCodeChange"
              />
              <p class="text-xs text-white/60 mt-1">
                If you have an existing session code, enter it here. Your
                username will be automatically filled.
              </p>
            </div>

            <!-- Username Field -->
            <div>
              <label
                for="username"
                class="block text-sm font-medium text-white/80 mb-2"
                >Username</label
              >
              <input
                id="username"
                v-model="username"
                name="username"
                type="text"
                autocomplete="username"
                :required="!existingSessionCode"
                :disabled="isSessionCodeValid"
                class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 disabled:opacity-50 disabled:cursor-not-allowed placeholder-white/40"
                placeholder="Enter your username"
              />
              <p v-if="isSessionCodeValid" class="text-xs text-green-400 mt-1">
                Username automatically filled from session code
              </p>
            </div>

            <!-- Email Field -->
            <div>
              <label
                for="email"
                class="block text-sm font-medium text-white/80 mb-2"
                >Email (Optional)</label
              >
              <input
                id="email"
                v-model="email"
                name="email"
                type="email"
                autocomplete="email"
                class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 placeholder-white/40"
                placeholder="Enter your email"
              />
            </div>

            <button
              type="submit"
              :disabled="
                isLoading || (!username.trim() && !existingSessionCode)
              "
              class="btn-phantom w-full px-6 py-3 text-base"
            >
              <span>{{
                isLoading
                  ? "Starting Quiz..."
                  : existingSessionCode
                    ? "Continue with Session"
                    : "Start Quiz"
              }}</span>
            </button>
          </form>

          <!-- Error Message -->
          <div
            v-if="errorMessage"
            class="mt-6 p-4 bg-red-500/10 border border-red-500/30 rounded-lg"
          >
            <p class="text-red-400 text-sm">
              {{ errorMessage }}
            </p>
          </div>
        </div>

        <!-- Quiz Interface -->
        <div
          v-else-if="!quizCompleted"
          class="card-phantom p-10 pb-16 shadow-glow-md quiz-content"
        >
          <!-- Quiz Header -->
          <div class="flex justify-between items-center mb-6">
            <div class="text-white">
              <h2 class="text-xl font-bold text-white">
                Session:
                <span class="text-phantom-blue font-mono">{{
                  sessionCode
                }}</span>
              </h2>
              <!-- Fullscreen indicator -->
              <div
                v-if="fullscreenQuiz.isFullscreen.value"
                class="flex items-center mt-2 text-sm text-green-400"
              >
                <svg
                  class="w-4 h-4 mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z"
                    clip-rule="evenodd"
                  />
                </svg>
                Fullscreen Mode Active
              </div>
            </div>
            <div class="text-right">
              <div class="text-white text-lg font-semibold">
                Question {{ currentQuestionIndex + 1 }}
                {{ isDynamicAssessment ? "" : `of ${maxQuestions}` }}
              </div>
            </div>
          </div>

          <!-- Timer -->
          <div class="mb-6 text-center">
            <div class="text-2xl font-bold text-phantom-blue">
              Time Remaining: {{ formatTime(timeRemaining) }}
            </div>
            <div class="w-full bg-white/10 rounded-full h-2 mt-2">
              <div
                class="h-2 rounded-full transition-all duration-1000 bg-gradient-to-r from-phantom-blue to-phantom-indigo"
                :style="{ width: `${(timeRemaining / totalQuizTime) * 100}%` }"
              />
            </div>
          </div>

          <!-- Question Display -->
          <div v-if="currentQuestion" class="mb-1">
            <div class="mb-6">
              <h3
                class="text-xl text-white font-medium leading-relaxed question-text"
              >
                {{ currentQuestion.question }}
              </h3>
            </div>

            <!-- Answer Options -->
            <div class="space-y-3">
              <button
                v-for="(option, key) in currentQuestion.options"
                :key="key"
                :disabled="answerSubmitted || timeRemaining <= 0"
                class="w-full p-4 text-left bg-white/5 border border-white/10 rounded-lg text-white hover:bg-white/10 hover:border-phantom-blue/50 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                :class="getAnswerButtonClass(key)"
                @click="selectAnswer(key)"
              >
                <div class="flex items-center">
                  <span
                    class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center text-sm font-semibold mr-4"
                    :class="getAnswerIconClass(key)"
                  >
                    {{ key.toUpperCase() }}
                  </span>
                  <span class="answer-option">{{ option }}</span>
                </div>
              </button>
            </div>

            <!-- Submit Button -->
            <div class="mt-6 flex justify-end items-center gap-4">
              <button
                v-if="selectedAnswer && !answerSubmitted"
                :disabled="
                  !selectedAnswer || answerSubmitted || timeRemaining <= 0
                "
                class="btn-phantom px-6 py-2"
                @click="submitCurrentAnswer"
              >
                <span>Submit Answer</span>
              </button>
            </div>
          </div>

          <!-- Loading State -->
          <div v-else class="text-center py-12">
            <div
              class="animate-spin rounded-full h-12 w-12 border-b-2 border-phantom-blue mx-auto mb-4"
            />
            <p class="text-white/70">Loading next question...</p>
          </div>

          <!-- No immediate feedback after answering questions -->
        </div>

        <!-- Quiz Results -->
        <div v-else class="card-phantom p-10 pb-16 shadow-glow-md">
          <div>
            <h2 class="text-3xl font-bold text-white mb-6 text-center">
              {{
                timeUp
                  ? "Time's Up!"
                  : quizSubmittedOnQuit
                    ? "Quiz Submitted!"
                    : "Quiz Completed!"
              }}
            </h2>
            <div
              v-if="timeUp"
              class="mb-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg"
            >
              <p class="text-yellow-400 text-sm">
                ⏰ The quiz time has expired. Your answers have been
                automatically submitted.
              </p>
            </div>
            <div
              v-else-if="quizSubmittedOnQuit"
              class="mb-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg"
            >
              <p class="text-blue-400 text-sm">
                📝 Your quiz session was submitted with your current progress.
                Your score has been calculated based on the questions you
                answered.
              </p>
            </div>

            <div
              class="bg-white/5 backdrop-blur-sm p-6 rounded-lg border border-white/10 mb-6"
            >
              <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div class="text-3xl font-bold text-phantom-blue">
                    {{ correctAnswers }}
                  </div>
                  <div class="text-white/70">Correct Answers</div>
                </div>
                <div>
                  <div class="text-3xl font-bold text-white">
                    {{ questionsAttempted }}
                  </div>
                  <div class="text-white/70">
                    {{
                      isDynamicAssessment
                        ? "Questions Attempted"
                        : "Total Questions"
                    }}
                  </div>
                </div>
                <div>
                  <div class="text-3xl font-bold text-phantom-indigo">
                    {{ currentScore.toFixed(1) }}
                  </div>
                  <div class="text-white/70">Total Score</div>
                </div>
              </div>

              <div class="mt-6 text-center">
                <div class="text-2xl font-bold text-white mb-2">
                  Percentage:
                  {{
                    questionsAttempted > 0
                      ? Math.round((correctAnswers / questionsAttempted) * 100)
                      : 0
                  }}%
                </div>
                <div class="text-lg text-white/80">
                  Performance:
                  <span class="text-phantom-indigo font-semibold">{{
                    getPerformanceLevel()
                  }}</span>
                </div>
              </div>
            </div>

            <!-- Detailed Results Button -->
            <div v-if="answeredQuestions.length > 0" class="mb-6">
              <button
                class="w-full py-4 px-6 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-phantom-blue/50 rounded-lg transition-all duration-200 flex items-center justify-center"
                @click="openDetailedResultsPopup"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-phantom-blue"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
                <span class="text-lg font-semibold btn-text-visible"
                  >View Detailed Results</span
                >
              </button>
            </div>

            <!-- Detailed Results Popup -->
            <div
              v-if="detailedResultsModal.isOpen.value"
              class="fixed inset-0 z-50 flex items-center justify-center p-4 pt-24 bg-black/80 backdrop-blur-sm animate-fadeIn"
              @click="detailedResultsModal.handleBackdropClick"
            >
              <div
                class="relative w-full max-w-2xl max-h-[75vh] bg-phantom-dark border border-white/10 rounded-lg shadow-glow-lg overflow-hidden flex flex-col animate-scaleIn"
                @click.stop
              >
                <!-- Popup Header -->
                <div
                  class="p-4 border-b border-white/10 flex justify-between items-center sticky top-0 bg-phantom-dark z-10 shadow-md backdrop-blur-sm"
                >
                  <h3 class="text-xl font-bold text-white flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 mr-2 text-phantom-blue"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                    Detailed Results
                  </h3>
                  <button
                    class="p-2 rounded-full bg-white/5 hover:bg-white/10 text-white/80 hover:text-white focus:outline-none transition-all duration-200 border border-white/10 hover:border-phantom-blue/50"
                    aria-label="Close popup"
                    @click="closeDetailedResultsPopup"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <!-- Popup Content - Scrollable Area -->
                <div class="overflow-y-auto p-4 flex-grow custom-scrollbar">
                  <div class="space-y-4">
                    <div
                      v-for="(question, index) in answeredQuestions"
                      :key="index"
                      class="bg-white/5 backdrop-blur-sm p-4 rounded-lg border border-white/10"
                    >
                      <div class="flex justify-between items-start mb-2">
                        <h4 class="text-white font-medium">
                          {{ index + 1 }}. {{ question.question }}
                        </h4>
                        <span
                          class="ml-2 px-2 py-1 rounded-full text-xs font-semibold"
                          :class="
                            question.isCorrect
                              ? 'bg-green-400/10 text-green-300'
                              : 'bg-red-400/10 text-red-300'
                          "
                        >
                          {{ question.isCorrect ? "Correct" : "Incorrect" }}
                        </span>
                      </div>

                      <div class="grid grid-cols-1 md:grid-cols-2 gap-2 mt-3">
                        <div
                          v-for="(option, key) in question.options"
                          :key="key"
                          class="p-3 rounded-lg border text-sm flex items-center"
                          :class="getResultOptionClass(key, question)"
                        >
                          <span
                            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold mr-2"
                            :class="getResultOptionIconClass(key, question)"
                          >
                            {{ key.toUpperCase() }}
                          </span>
                          <span>{{ option }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex space-x-4 justify-center">
              <button
                class="btn-phantom-secondary px-6 py-3"
                @click="goBackToSession"
              >
                <span>Back to Session</span>
              </button>
              <button class="btn-phantom px-6 py-3" @click="restartQuiz">
                <span>Take Another Quiz</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Fullscreen Quit Modal -->
    <div
      v-if="fullscreenQuiz.showQuitModal.value"
      class="fullscreen-quit-modal flex items-center justify-center"
    >
      <div class="modal-content text-center p-8 max-w-md mx-auto">
        <div class="mb-6">
          <svg
            class="w-20 h-20 mx-auto text-red-400 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <h2 class="text-3xl font-bold text-white mb-4">Quiz Session Ending</h2>
        <p class="text-xl text-white/80 mb-6">
          You attempted to leave the quiz session.
        </p>
        <p class="text-lg text-red-400 mb-8">
          Your quiz is being submitted automatically...
        </p>
        <div class="flex justify-center mb-4">
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-400"
          ></div>
        </div>
        <p class="text-sm text-white/60 mb-4">
          You will be redirected shortly...
        </p>
        <button
          class="text-white/60 hover:text-white text-sm underline"
          @click="fullscreenQuiz.closeQuitModal()"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRoute, useRouter, onBeforeRouteLeave } from "vue-router";
import { api, request } from "@/services/api";
import {
  getErrorMessage,
  logError,
  logQuizQuit,
  logQuizSubmissionError,
  confirmQuizQuit,
} from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { debug, info, warning, error, logApiResponse } from "@/utils/logger";
import { useDetailedResultsModal, useFullscreenQuiz } from "@/composables";
import { useQuizSession } from "@/composables/useQuizSession";
import { useQuizTimer } from "@/composables/useQuizTimer";
import {
  decodeSessionCodeFromHash,
  decodeAssessmentId,
  isHashId,
  extractSessionCode,
} from "@/utils/hashIds";

const route = useRoute();
const router = useRouter();

// Message handling
const { setErrorMessage, clearMessage } = useMessageHandler();

// Modal handling
const detailedResultsModal = useDetailedResultsModal();

// Fullscreen quiz handling
const fullscreenQuiz = useFullscreenQuiz(() => {
  if (quizStarted.value && !quizCompleted.value) {
    submitQuizOnQuit();
  }
});

// New composables for better organization
const quizSession = useQuizSession();
const quizTimer = useQuizTimer();

// Reactive data
const assessmentId = ref(null);
const assessmentName = ref("");
const assessmentInfo = ref(null);
const username = ref("");
const email = ref("");
const isLoading = ref(false);
const quizStarted = ref(false);
const sessionCode = ref("");
const existingSessionCode = ref("");
const isSessionCodeValid = ref(false);
const sessionCodeCheckTimeout = ref(null);
const errorMessage = ref("");
const isRouteHashId = ref(false);
const isDirectStart = ref(false);

// Quiz state
const quizCompleted = ref(false);
const currentQuestion = ref(null);
const currentQuestionIndex = ref(0);
const selectedAnswer = ref("");
const answerSubmitted = ref(false);
const lastAnswerCorrect = ref(false);
const timeUp = ref(false);
const quizSubmittedOnQuit = ref(false); // Track if quiz was submitted due to user quit
const correctAnswers = ref(0);
const questionsAttempted = ref(0);
const currentScore = ref(0); // Track the actual score from backend
const maxQuestions = ref(20); // Default, will be updated based on assessment type
const totalQuizTime = ref(3600); // Default 60 minutes in seconds (60 * 60)
const timeRemaining = ref(3600);
const timerInterval = ref(null);
const currentDifficulty = ref("easy");
const difficultyProgression = ["easy", "intermediate", "advanced"];
const allQuestions = ref([]); // Store all questions fetched from API
const answeredQuestions = ref([]); // Store questions with user answers for results
const questionsLoaded = ref(false);
const questionStartTime = ref(null); // Track when current question was displayed
const currentCorrectAnswer = ref(""); // Store the correct answer text for current question
const currentCorrectAnswerKey = ref(""); // Store the correct answer key for current question

const isResuming = ref(false); // Track if we're resuming an existing session
const lastActiveTime = ref(null); // Track when the page was last active
const isDynamicAssessment = ref(false); // Track if this is a dynamic assessment
const isAssessmentInfoFetching = ref(false); // Flag to prevent concurrent assessment info fetches
const lastSessionValidationTime = ref(null); // Track when session was last validated
const sessionValidationData = ref(null); // Store last session validation data

// Simplified caching removed - using direct API calls

// Methods
// Simplified timer update from backend
const updateTimeFromBackend = (backendTime, context = "unknown") => {
  if (
    backendTime === undefined ||
    backendTime === null ||
    isNaN(Number(backendTime))
  ) {
    debug(`Invalid backend time in ${context}`, { backendTime });
    return false;
  }

  const validBackendTime = Math.max(0, Math.floor(Number(backendTime)));
  const maxTime = Math.floor(Number(totalQuizTime.value) || 3600);

  if (validBackendTime > maxTime) {
    warning(`Backend time exceeds max time in ${context}`, {
      backendTime: validBackendTime,
      maxTime,
    });
    return false;
  }

  timeRemaining.value = validBackendTime;
  debug(`Updated timer from backend in ${context}`, {
    newTime: validBackendTime,
    context,
  });
  return true;
};

const isSessionValidationNeeded = () => {
  // If we haven't validated recently (within last 30 seconds), validate again
  if (!lastSessionValidationTime.value || !sessionValidationData.value) {
    return true;
  }

  const timeSinceLastValidation = Date.now() - lastSessionValidationTime.value;
  const VALIDATION_CACHE_TIME = 30000; // 30 seconds

  return timeSinceLastValidation > VALIDATION_CACHE_TIME;
};

// Simplified session user data function
const getSessionUser = async (sessionCode) => {
  debug("Fetching session user data", { sessionCode });
  const userResponse = await api.admin.getSessionUser(sessionCode);
  return extractResponseData(userResponse);
};

// Simplified assessment data function
const getAssessment = async (assessmentId) => {
  debug("Fetching assessment data", { assessmentId });
  const response = await api.admin.getAssessment(assessmentId, false);
  return extractResponseData(response);
};

// Function to invalidate session validation cache
const invalidateSessionValidationCache = () => {
  debug("Invalidating session validation cache");
  lastSessionValidationTime.value = null;
  sessionValidationData.value = null;
};

const validateSessionCodeCached = async (sessionCode) => {
  // Use cached data if available and recent
  if (!isSessionValidationNeeded() && sessionValidationData.value) {
    debug("Using cached session validation data", {
      sessionCode,
      cachedAt: lastSessionValidationTime.value,
    });
    return sessionValidationData.value;
  }

  // Perform fresh validation
  debug("Performing fresh session validation", { sessionCode });
  const sessionResponse = await api.quiz.validateSessionCode({
    session_code: sessionCode,
  });
  logApiResponse(
    "POST",
    "/api/validate_session_code",
    sessionResponse?.status || 200,
    sessionResponse?.data,
  );

  const sessionData = extractResponseData(sessionResponse);

  // Cache the validation data
  if (sessionData) {
    sessionValidationData.value = sessionData;
    lastSessionValidationTime.value = Date.now();
  }

  return sessionData;
};

const fetchAssessmentInfo = async () => {
  try {
    // Validate assessment ID before making API call
    if (
      !assessmentId.value ||
      isNaN(assessmentId.value) ||
      assessmentId.value <= 0
    ) {
      throw new Error("Invalid assessment ID");
    }

    // Check if we already have complete assessment info to avoid duplicate calls
    if (
      assessmentInfo.value &&
      assessmentInfo.value.duration_minutes &&
      assessmentInfo.value.total_questions &&
      assessmentInfo.value.name
    ) {
      debug("Assessment info already loaded, skipping API call", {
        assessmentId: assessmentId.value,
        name: assessmentInfo.value.name,
        duration: assessmentInfo.value.duration_minutes,
        totalQuestions: assessmentInfo.value.total_questions,
      });
      return;
    }

    // Check if another fetch is already in progress
    if (isAssessmentInfoFetching.value) {
      debug(
        "Assessment info fetch already in progress, skipping duplicate call",
      );
      return;
    }

    // Set flag to prevent concurrent fetches
    isAssessmentInfoFetching.value = true;

    const data = await getAssessment(assessmentId.value);

    if (data) {
      assessmentInfo.value = data;
      assessmentName.value = data.name;

      // Set max questions from assessment data
      maxQuestions.value = data.total_questions; // Use total_questions from assessment

      // Check if this is a dynamic assessment
      isDynamicAssessment.value = data.question_selection_mode === "dynamic";

      // Set quiz duration from assessment (convert minutes to seconds)
      const durationMinutes = Math.max(
        1,
        Math.floor(Number(data.duration_minutes) || 60),
      );
      totalQuizTime.value = durationMinutes * 60;

      // Only reset timeRemaining if quiz hasn't started yet
      if (!quizStarted.value) {
        timeRemaining.value = totalQuizTime.value;
        debug("Setting initial time from assessment", {
          durationMinutes,
          totalSeconds: totalQuizTime.value,
        });
      }
    } else {
      throw new Error("Failed to extract assessment data from response");
    }
  } catch (error) {
    logError(error, "fetchAssessmentInfo");
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(
      errorInfo.message || "Failed to load assessment information",
    );
  } finally {
    // Always reset the flag
    isAssessmentInfoFetching.value = false;
  }
};

// Quiz functionality
const startTimer = () => {
  timeUp.value = false;
  lastActiveTime.value = Date.now();

  // Clear any existing timer
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }

  // Load saved state if available
  const hasLoadedState = loadTimerState();

  // Set initial time if not loaded from state
  if (!hasLoadedState && !isResuming.value) {
    timeRemaining.value = Math.floor(Number(totalQuizTime.value) || 3600);
  }

  // Validate time remaining
  timeRemaining.value = Math.max(
    0,
    Math.min(
      Math.floor(Number(timeRemaining.value) || 0),
      Math.floor(Number(totalQuizTime.value) || 3600),
    ),
  );

  if (timeRemaining.value <= 0) {
    timeRemaining.value = Math.floor(Number(totalQuizTime.value) || 3600);
  }

  saveTimerState();
  startTimerInterval();
};

const stopTimer = () => {
  if (timerInterval.value) {
    debug("Stopping timer interval");
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
};

// Ensure timer continues running
const ensureTimerContinuity = () => {
  if (quizStarted.value && !quizCompleted.value && timeRemaining.value > 0) {
    if (!timerInterval.value) {
      startTimerInterval();
    }
  } else if (
    timerInterval.value &&
    (quizCompleted.value || timeRemaining.value <= 0)
  ) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
};

const startTimerInterval = () => {
  if (timerInterval.value || !quizStarted.value || quizCompleted.value) {
    return;
  }

  timeRemaining.value = Math.max(
    0,
    Math.floor(Number(timeRemaining.value) || 0),
  );

  timerInterval.value = setInterval(() => {
    if (quizStarted.value && !quizCompleted.value && timeRemaining.value > 0) {
      timeRemaining.value = Math.max(0, timeRemaining.value - 1);
      lastActiveTime.value = Date.now();

      // Save state every 10 seconds
      if (timeRemaining.value % 10 === 0) {
        saveTimerState();
      }

      // Check if time is up
      if (timeRemaining.value <= 0) {
        clearInterval(timerInterval.value);
        timerInterval.value = null;
        timeUp.value = true;
        completeQuiz();
      }
    }
  }, 1000);
};

const saveTimerState = () => {
  if (!sessionCode.value || timeRemaining.value <= 0) {
    return;
  }

  const timerState = {
    timeRemaining: Math.max(0, Math.floor(Number(timeRemaining.value))),
    lastActiveTime: lastActiveTime.value || Date.now(),
    sessionCode: sessionCode.value,
    savedAt: Date.now(),
  };

  try {
    localStorage.setItem(
      `quiz_timer_${sessionCode.value}`,
      JSON.stringify(timerState),
    );
  } catch (err) {
    error("Error saving timer state", { error: err });
  }
};

const loadTimerState = () => {
  if (!sessionCode.value) {
    return false;
  }

  const savedState = localStorage.getItem(`quiz_timer_${sessionCode.value}`);
  if (!savedState) {
    return false;
  }

  try {
    const timerState = JSON.parse(savedState);

    if (timerState.sessionCode !== sessionCode.value) {
      return false;
    }

    const savedTimeRemaining = Math.max(
      0,
      Math.floor(Number(timerState.timeRemaining) || 0),
    );
    const maxTime = Math.floor(Number(totalQuizTime.value) || 3600);

    if (savedTimeRemaining <= 0 || savedTimeRemaining > maxTime) {
      return false;
    }

    // Check if state is too old (1 hour)
    const now = Date.now();
    const timeSinceLastActive = now - (timerState.lastActiveTime || now);
    if (timeSinceLastActive > 3600000) {
      return false;
    }

    timeRemaining.value = savedTimeRemaining;
    lastActiveTime.value = timerState.lastActiveTime;
    return true;
  } catch (err) {
    error("Error loading timer state", { error: err });
    return false;
  }
};

const clearTimerState = () => {
  if (sessionCode.value) {
    localStorage.removeItem(`quiz_timer_${sessionCode.value}`);
  }
};

// Page visibility handlers
const handleBeforeUnload = () => {
  if (quizStarted.value && !quizCompleted.value && timeRemaining.value > 0) {
    saveTimerState();
    submitQuizOnQuit();
  }
};

// Function to submit quiz when user quits
const submitQuizOnQuit = async () => {
  if (!sessionCode.value || !username.value || quizCompleted.value) {
    return;
  }

  try {
    stopTimer();
    clearTimerState();
    quizCompleted.value = true;
    quizSubmittedOnQuit.value = true;

    const submissionData = {
      session_code: sessionCode.value,
      user_id: username.value,
    };

    if (navigator.sendBeacon) {
      const submitData = JSON.stringify(submissionData);
      const blob = new Blob([submitData], { type: "application/json" });
      navigator.sendBeacon("/api/submit_session", blob);
    } else {
      await api.quiz.submitSession(submissionData);
    }

    // Hide quit modal after successful submission
    setTimeout(() => {
      fullscreenQuiz.closeQuitModal();
    }, 1000);
  } catch (err) {
    logQuizSubmissionError(err, "quit_handler");
    // Hide quit modal even if submission fails
    setTimeout(() => {
      fullscreenQuiz.closeQuitModal();
    }, 2000);
  }
};

const setupPageVisibilityListeners = () => {
  // Only add beforeunload listener to warn user about leaving
  window.addEventListener("beforeunload", handleBeforeUnload);
};

const removePageVisibilityListeners = () => {
  window.removeEventListener("beforeunload", handleBeforeUnload);
};

const fetchNextQuestion = async () => {
  try {
    debug("fetchNextQuestion called", {
      sessionCode: sessionCode.value,
      username: username.value,
      currentDifficulty: currentDifficulty.value,
    });

    // Make sure we have both session code and username
    if (!sessionCode.value) {
      error("Session code is missing");
      setErrorMessage("Session code is required. Please restart the quiz.");
      return;
    }

    // Make sure we have a username before proceeding
    if (!username.value) {
      error("Username is missing");
      setErrorMessage("Username is required. Please restart the quiz.");
      return;
    }

    // Fetch the next question

    const response = await request.get(
      `/get_next_question/${sessionCode.value}`,
      {
        user_id: username.value,
        difficulty: currentDifficulty.value,
      },
    );

    // Extract data using the standardized response handler
    const data = extractResponseData(response);

    // Check if response has a question
    if (!data?.question) {
      error("No question found in response data");
      // No more questions available, end quiz
      completeQuiz();
      return;
    }

    // Set the current question
    currentQuestion.value = data.question;

    // Check if this is a dynamic assessment
    if (data.question_selection_mode) {
      isDynamicAssessment.value = data.question_selection_mode === "dynamic";
    }

    // Update quiz metadata
    if (data.total_questions) {
      maxQuestions.value = data.total_questions;
    }
    if (data.attempted_questions_count !== undefined) {
      questionsAttempted.value = data.attempted_questions_count;

      // If we have attempted questions, we're resuming
      if (data.attempted_questions_count > 0) {
        isResuming.value = true;
        // Set the current question index to the number of attempted questions
        // This ensures the question counter shows correctly
        currentQuestionIndex.value = data.attempted_questions_count;
        // When resuming, correct answers count will be updated from session details
      }
    }

    // Update remaining time from backend only if timer hasn't started yet
    if (!timerInterval.value) {
      updateTimeFromBackend(data.remaining_time_seconds, "fetchNextQuestion");
    }

    // Update current score from backend if available
    if (data.current_score !== undefined && data.current_score !== null) {
      currentScore.value = data.current_score;
    }

    // Reset question state
    selectedAnswer.value = "";
    answerSubmitted.value = false;
    currentCorrectAnswer.value = "";
    currentCorrectAnswerKey.value = "";

    // Record the start time for this question
    questionStartTime.value = Date.now();

    // Only start timer for the very first question when quiz is just starting
    // Don't restart timer during normal question transitions
    if (
      !timerInterval.value &&
      questionsAttempted.value === 0 &&
      !isResuming.value
    ) {
      setTimeout(() => {
        startTimer();
      }, 100);
    }
  } catch (err) {
    error("Error in fetchNextQuestion", { error: err });
    if (err.response?.status === 404) {
      completeQuiz();
    } else {
      logError(err, "fetchNextQuestion");
      setErrorMessage(getErrorMessage(err, "Failed to load next question"));
    }
  }
};

// Helper functions for initializeQuizSession
const validateSessionCodeExists = () => {
  if (!sessionCode.value) {
    setErrorMessage("Session code is required. Please restart the quiz.");
    return false;
  }
  return true;
};

const validateAndUpdateSessionData = async () => {
  // Check if we already have session validation data and it's recent
  const shouldValidateSession = !isDynamicAssessment.value || !assessmentId.value;

  if (shouldValidateSession) {
    try {
      const sessionData = await validateSessionCodeCached(sessionCode.value);
      if (sessionData) {
        // Check if this is a dynamic assessment
        isDynamicAssessment.value = sessionData.question_selection_mode === "dynamic";
      }
      return sessionData;
    } catch (error) {
      logError(error, "checkSessionStatus");
      const errorInfo = extractErrorInfo(error);
      setErrorMessage(
        errorInfo.message || "Error validating session. Please restart the quiz.",
      );
      throw error;
    }
  }
  return null;
};

const processAssessmentId = async (rawAssessmentId) => {
  // Check if assessment_id is a hash
  if (isHashId(rawAssessmentId)) {
    try {
      const decodedId = await decodeAssessmentId(rawAssessmentId);
      if (decodedId) {
        return decodedId;
      } else {
        throw new Error(`Failed to decode assessment hash: ${rawAssessmentId}`);
      }
    } catch (error) {
      logError(error, "decodeAssessmentIdFromSession");
      setErrorMessage("Failed to load assessment information from session.");
      throw error;
    }
  } else {
    // It's a regular ID
    const parsedId = parseInt(rawAssessmentId);
    if (!isNaN(parsedId) && parsedId > 0) {
      return parsedId;
    } else {
      logError(
        new Error(`Invalid assessment ID from session: ${rawAssessmentId}`),
        "parseAssessmentIdFromSession",
      );
      setErrorMessage("Invalid assessment information from session.");
      throw new Error("Invalid assessment ID");
    }
  }
};

const fetchAndUpdateUserData = async () => {
  // If we don't have a username or assessment ID, try to fetch them
  if (!username.value || !assessmentId.value) {
    try {
      const userData = await getSessionUser(sessionCode.value);

      if (userData && userData.username) {
        username.value = userData.username;
        // Also update assessment info if available
        if (userData.assessment_id) {
          const processedId = await processAssessmentId(userData.assessment_id);
          assessmentId.value = processedId;
          assessmentName.value = userData.assessment_name || "";
        }
      } else {
        setErrorMessage("Could not retrieve username for this session.");
        throw new Error("No username found");
      }
    } catch (err) {
      logError(err, "fetchUsername");
      setErrorMessage("Error retrieving username. Please restart the quiz.");
      throw err;
    }
  }
};

const initializeQuizSession = async () => {
  try {
    if (!validateSessionCodeExists()) {
      return;
    }

    // Skip session validation if already done in startQuiz or checkSessionCode
    await validateAndUpdateSessionData();

    // Fetch user data if needed
    await fetchAndUpdateUserData();

    // Fetch the first question
    await fetchNextQuestion();

    // Ensure timer is running after initialization
    ensureTimerContinuity();
  } catch (err) {
    logError(err, "initializeQuizSession");
    setErrorMessage(getErrorMessage(err, "Failed to initialize quiz session"));
  }
};

const showNextQuestion = async () => {
  // For dynamic assessments, only time limit matters, not question count
  // For fixed assessments, check if we've reached the maximum number of questions
  if (
    !isDynamicAssessment.value &&
    questionsAttempted.value >= maxQuestions.value
  ) {
    completeQuiz();
    return;
  }

  // Increment the question index
  currentQuestionIndex.value++;

  // Ensure timer continues running during question transition
  ensureTimerContinuity();

  // Fetch the next question from the server
  await fetchNextQuestion();

  // Ensure timer is still running after fetching the question
  ensureTimerContinuity();
};

const selectAnswer = (answerKey) => {
  if (answerSubmitted.value || timeRemaining.value <= 0) return;

  selectedAnswer.value = answerKey;
  // Don't auto-submit, wait for manual submit button click
};

const submitCurrentAnswer = () => {
  if (!selectedAnswer.value || answerSubmitted.value) return;
  submitAnswer(selectedAnswer.value);
};

const submitAnswer = async (answer) => {
  if (answerSubmitted.value) return;

  answerSubmitted.value = true;
  // Don't stop the overall timer, just continue with the quiz

  // Make sure we have both session code and username
  if (!sessionCode.value) {
    setErrorMessage("Session code is required. Please restart the quiz.");
    return;
  }

  // Check session status before submitting answer - use cached validation
  try {
    const sessionData = await validateSessionCodeCached(sessionCode.value);
    if (sessionData) {
      const sessionStatus = sessionData.session_status;

      // Check if session is completed
      if (sessionStatus === "completed") {
        setErrorMessage(
          "This session has already been completed. Cannot submit more answers.",
        );
        completeQuiz();
        return;
      }

      // Check if session is expired
      if (sessionStatus === "expired") {
        setErrorMessage(
          "This session has expired. Cannot submit more answers.",
        );
        completeQuiz();
        return;
      }

      // Check if session is in a valid state (in_progress)
      if (sessionStatus !== "in_progress") {
        // If session is still pending, try to start it first
        if (sessionStatus === "pending") {
          try {
            const startSessionResponse = await api.quiz.startSession({
              session_code: sessionCode.value,
            });

            const startSessionData = extractResponseData(startSessionResponse);
            if (!startSessionData) {
              throw new Error("Failed to start session");
            }

            // Invalidate session validation cache since session status has changed
            invalidateSessionValidationCache();

            // Add a small delay to ensure session state is updated
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Re-validate the session - use fresh validation
            const revalidateData = await validateSessionCodeCached(
              sessionCode.value,
            );
            if (
              !revalidateData ||
              revalidateData.session_status !== "in_progress"
            ) {
              throw new Error("Session failed to start properly");
            }
          } catch (startError) {
            logError(startError, "startSessionFromSubmit");
            setErrorMessage(
              "Failed to start session. Please refresh and try again.",
            );
            return;
          }
        } else {
          setErrorMessage(
            `Session is not in progress. Status: ${sessionStatus}`,
          );
          completeQuiz();
          return;
        }
      }
    }
  } catch (err) {
    logError(err, "checkSessionStatusSubmit");
    const errorInfo = extractErrorInfo(err);
    setErrorMessage(
      errorInfo.message || "Error validating session. Cannot submit answer.",
    );
    return;
  }

  try {
    // Calculate time taken for this question
    const currentTime = Date.now();
    const timeTakenMs = questionStartTime.value
      ? currentTime - questionStartTime.value
      : 0;
    const timeTakenSeconds = Math.round(timeTakenMs / 1000); // Convert to seconds and round

    const answerData = {
      user_id: username.value,
      question_id: currentQuestion.value.que_id.toString(),
      answer: answer,
      session_code: sessionCode.value,
      time_taken: timeTakenSeconds,
    };

    const response = await api.quiz.checkAndSaveAnswer(answerData);
    const data = extractResponseData(response);

    if (!data) {
      throw new Error("Failed to extract data from response");
    }

    const isCorrect = data.is_correct;
    lastAnswerCorrect.value = isCorrect;

    // Store the correct answer text and key from the API response
    if (data.correct_answer_value) {
      currentCorrectAnswer.value = data.correct_answer_value;
    } else if (data.correct_answer_key && currentQuestion.value?.options) {
      // Fallback: use the correct answer key to get the text from options
      currentCorrectAnswer.value =
        currentQuestion.value.options[data.correct_answer_key] || "";
    }

    // Store the correct answer key for highlighting purposes
    if (data.correct_answer_key) {
      currentCorrectAnswerKey.value = data.correct_answer_key;
    }

    // Check if this is a dynamic assessment
    if (data.question_selection_mode) {
      isDynamicAssessment.value = data.question_selection_mode === "dynamic";
    }

    // Update score and timing from backend response
    if (data.current_score !== undefined && data.current_score !== null) {
      currentScore.value = data.current_score;
    }

    // Don't update timer from backend during quiz - let local timer continue
    // updateTimeFromBackend(data.remaining_time_seconds, "submitAnswer");

    if (data.attempted_questions_count !== undefined) {
      questionsAttempted.value = data.attempted_questions_count;
    }

    // Keep the local correct answers count for UI feedback
    if (isCorrect) {
      correctAnswers.value++;
    }

    // Store the question and answer for detailed results
    if (currentQuestion.value) {
      answeredQuestions.value.push({
        question: currentQuestion.value.question,
        options: currentQuestion.value.options,
        userAnswer: answer,
        correctAnswerKey: data.correct_answer_key,
        correctAnswer: currentCorrectAnswer.value,
        isCorrect: isCorrect,
      });
    }

    // Progress difficulty based on performance
    updateDifficulty(isCorrect);

    // Ensure timer continues running after answer submission
    ensureTimerContinuity();

    // Move to next question immediately without showing feedback
    // For dynamic assessments, only time limit matters, not question count
    // For fixed assessments, check if we've reached the maximum number of questions
    if (
      !isDynamicAssessment.value &&
      questionsAttempted.value >= maxQuestions.value
    ) {
      completeQuiz();
    } else {
      await showNextQuestion();
    }
  } catch (err) {
    logError(err, "submitAnswer");
    const errorInfo = extractErrorInfo(err);

    // If it's a server error, try to continue the quiz
    if (err.response?.status === 500) {
      warning("Server error during answer submission, continuing quiz", {
        error: err,
      });
      // Continue to next question even if answer submission failed
      await showNextQuestion();
    } else {
      setErrorMessage(errorInfo.message || "Failed to submit answer");
    }
  }
};

const updateDifficulty = (isCorrect) => {
  const currentIndex = difficultyProgression.indexOf(currentDifficulty.value);

  if (isCorrect && currentIndex < difficultyProgression.length - 1) {
    currentDifficulty.value = difficultyProgression[currentIndex + 1];
  } else if (!isCorrect && currentIndex > 0) {
    currentDifficulty.value = difficultyProgression[currentIndex - 1];
  }
};

const completeQuiz = async () => {
  stopTimer();
  clearTimerState();

  // Cleanup fullscreen mode
  await fullscreenQuiz.cleanupFullscreenQuiz();

  // Submit the session to mark it as completed
  if (sessionCode.value && username.value) {
    try {
      // Log the data being sent for debugging
      const submissionData = {
        session_code: sessionCode.value,
        user_id: username.value,
      };

      info("Submitting session with data:", submissionData);

      // First, validate the session status before submitting - use cached validation
      try {
        const sessionData = await validateSessionCodeCached(sessionCode.value);

        if (sessionData) {
          info("Session validation before submission:", {
            status: sessionData.session_status,
            remaining_time: sessionData.remaining_time_seconds,
            assessment_id: sessionData.assessment_id,
          });

          // Check if session is in a valid state for submission
          if (sessionData.session_status === "completed") {
            warning("Session is already completed, skipping submission");
            quizCompleted.value = true;
            return;
          }

          if (sessionData.session_status === "expired") {
            warning("Session has expired, skipping submission");
            quizCompleted.value = true;
            return;
          }

          if (sessionData.session_status !== "in_progress") {
            warning("Session is not in progress, cannot submit", {
              current_status: sessionData.session_status,
            });
            quizCompleted.value = true;
            return;
          }
        }
      } catch (validationError) {
        error("Error validating session before submission:", validationError);
        // Continue with submission attempt anyway
      }

      const response = await api.quiz.submitSession(submissionData);
      // Response is logged by axios interceptor, no need for manual logging
      extractResponseData(response); // Extract for validation but don't store unused data
    } catch (error) {
      // Log the error for debugging
      error("Error submitting session:", error);
      logError(error, "submitSession");

      // Log detailed error information
      if (error.response) {
        error("Submit session error details:", {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers,
        });

        // Check if it's a specific backend error we can handle
        if (error.response.status === 400 && error.response.data?.detail) {
          warning(
            "Backend returned validation error:",
            error.response.data.detail,
          );

          // Handle specific error cases
          const errorDetail = error.response.data.detail;
          if (errorDetail.includes("Session is not in progress")) {
            info(
              "Session is not in progress, likely already completed or expired",
            );
          } else if (errorDetail.includes("User does not match session")) {
            warning(
              "User mismatch error - this should not happen in normal flow",
            );
          } else if (errorDetail.includes("Session code is required")) {
            warning("Session code missing - this indicates a frontend bug");
          }
        } else if (error.response.status === 403) {
          warning("Session submission forbidden - user mismatch");
        } else if (error.response.status === 404) {
          warning("Session not found - may have been deleted or expired");
        }
      } else if (error.request) {
        error("Submit session request error:", error.request);
      } else {
        error("Submit session general error:", error.message);
      }

      // Don't prevent quiz completion if submission fails
      // But log a user-friendly message
      info(
        "Quiz completed locally, but session submission failed. Your progress may not be saved on the server.",
      );
    }
  } else {
    // Log warning if session code or username is missing
    warning("Cannot submit session: missing session code or username", {
      sessionCode: sessionCode.value,
      username: username.value,
    });

    // If we have a session code but no username, it might be a technical issue
    if (sessionCode.value && !username.value) {
      warning(
        "Session code exists but username is missing - this may indicate a bug in the flow",
      );
    }
  }

  // Always complete the quiz to prevent user frustration
  quizCompleted.value = true;
  info("Quiz completed successfully (local completion)");
};

const getAnswerButtonClass = (key) => {
  return selectedAnswer.value === key
    ? "bg-phantom-blue/20 border-phantom-blue"
    : "";
};

const getAnswerIconClass = (key) => {
  return selectedAnswer.value === key ? "bg-phantom-blue text-white" : "";
};

// Methods for detailed results display
const getResultOptionClass = (key, question) => {
  if (key === question.userAnswer && key === question.correctAnswerKey) {
    return "bg-green-400/5 border-green-400/20 text-white";
  } else if (key === question.userAnswer) {
    return "bg-red-400/5 border-red-400/20 text-white";
  } else if (key === question.correctAnswerKey) {
    return "bg-green-400/5 border-green-400/20 text-white";
  } else {
    return "bg-white/5 border-white/10 text-white/70";
  }
};

const getResultOptionIconClass = (key, question) => {
  if (key === question.userAnswer && key === question.correctAnswerKey) {
    return "bg-green-400 text-white";
  } else if (key === question.userAnswer) {
    return "bg-red-400 text-white";
  } else if (key === question.correctAnswerKey) {
    return "bg-green-400 text-white";
  } else {
    return "bg-white/10 text-white/70";
  }
};

const getPerformanceLevel = () => {
  if (questionsAttempted.value === 0) return "Fail";

  const percentage = (correctAnswers.value / questionsAttempted.value) * 100;

  if (percentage === 0) return "Fail";
  if (percentage < 33) return "Basic";
  if (percentage < 62) return "Acceptable";
  if (percentage < 85) return "Exceed Expectation";
  return "OUTSTANDING";
};

// Detailed results popup methods (using composable)
const openDetailedResultsPopup = detailedResultsModal.open;
const closeDetailedResultsPopup = detailedResultsModal.close;

const formatTime = (seconds) => {
  const totalSeconds = Math.max(0, Math.floor(Number(seconds) || 0));
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const secs = totalSeconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }
  return `${minutes}:${secs.toString().padStart(2, "0")}`;
};

const goBackToSession = () => {
  router.push("/user-sessions");
};

const restartQuiz = async () => {
  // First stop any running timers
  stopTimer();

  // Cleanup fullscreen mode
  await fullscreenQuiz.cleanupFullscreenQuiz();

  // Reset quit attempts
  fullscreenQuiz.resetQuitAttempts();

  quizCompleted.value = false;
  quizStarted.value = false;
  currentQuestion.value = null;
  currentQuestionIndex.value = 0;
  selectedAnswer.value = "";
  answerSubmitted.value = false;

  lastAnswerCorrect.value = false;
  timeUp.value = false;
  correctAnswers.value = 0;
  questionsAttempted.value = 0;
  currentScore.value = 0;
  quizSubmittedOnQuit.value = false;
  currentDifficulty.value = "easy";
  sessionCode.value = "";
  existingSessionCode.value = "";
  isSessionCodeValid.value = false;
  username.value = "";
  email.value = "";
  clearMessage();
  allQuestions.value = [];
  answeredQuestions.value = [];
  questionsLoaded.value = false;
  questionStartTime.value = null;
  currentCorrectAnswer.value = "";
  currentCorrectAnswerKey.value = "";

  // Ensure timer is reset to a valid integer value
  timeRemaining.value = Math.floor(Number(totalQuizTime.value) || 3600);

  // Reset timer state
  isResuming.value = false;
  lastActiveTime.value = null;

  // Clear cached session validation data
  lastSessionValidationTime.value = null;
  sessionValidationData.value = null;

  // Clear any timer state from localStorage
  try {
    localStorage.removeItem("quiz_timer_temp");
  } catch (err) {
    error("Error clearing temporary timer state", { error: err });
  }

  debug("Quiz state fully reset", {
    timeRemaining: timeRemaining.value,
    totalQuizTime: totalQuizTime.value,
  });
};

const checkSessionCode = async (code) => {
  if (!code || code.length !== 6) {
    isSessionCodeValid.value = false;
    username.value = "";
    return false;
  }

  try {
    // Use cached validation to avoid duplicate calls
    const sessionData = await validateSessionCodeCached(code);

    if (sessionData) {
      const sessionStatus = sessionData.session_status;

      // Check if session is completed
      if (sessionStatus === "completed") {
        setErrorMessage(
          "This session has already been completed and cannot be used again.",
        );
        isSessionCodeValid.value = false;
        username.value = "";
        return false;
      }

      // Check if session is expired
      if (sessionStatus === "expired") {
        setErrorMessage("This session has expired and cannot be used.");
        isSessionCodeValid.value = false;
        username.value = "";
        return false;
      }

      // Check if session is in a valid state (pending or in_progress)
      if (sessionStatus !== "pending" && sessionStatus !== "in_progress") {
        setErrorMessage(`Session is not available. Status: ${sessionStatus}`);
        isSessionCodeValid.value = false;
        username.value = "";
        return false;
      }

      // Now fetch the username from the session code
      const userData = await getSessionUser(code);

      if (userData && userData.username) {
        // Set the username from the response
        username.value = userData.username;
        isSessionCodeValid.value = true;
        clearMessage();

        // Update assessment info - check multiple possible field names
        let rawAssessmentId =
          userData.assessment_id ||
          userData.assessmentId ||
          userData.assessment_id_hash ||
          userData.assessmentIdHash;

        // Handle case where assessment ID is not available in user data
        if (!rawAssessmentId) {
          // Try to get assessment ID from the session data itself
          const sessionAssessmentId =
            sessionData.assessment_id ||
            sessionData.assessmentId ||
            sessionData.assessment_id_hash ||
            sessionData.assessmentIdHash;

          if (sessionAssessmentId) {
            rawAssessmentId = sessionAssessmentId;
          } else {
            // Continue without assessment ID - it might be set via route params
          }
        }

        // Only process assessment ID if we have one
        if (rawAssessmentId) {
          // Check if assessment_id is a hash
          if (isHashId(rawAssessmentId)) {
            // It's a hash, decode it
            try {
              const decodedId = await decodeAssessmentId(rawAssessmentId);
              if (decodedId) {
                assessmentId.value = decodedId;
              } else {
                throw new Error(
                  `Failed to decode assessment hash: ${rawAssessmentId}`,
                );
              }
            } catch (error) {
              logError(error, "decodeAssessmentIdInCheckSession");
              setErrorMessage("Failed to load assessment information.");
              return false;
            }
          } else {
            // It's a regular ID
            const parsedId = parseInt(rawAssessmentId);
            if (!isNaN(parsedId) && parsedId > 0) {
              assessmentId.value = parsedId;
            } else {
              logError(
                new Error(
                  `Invalid assessment ID from checkSessionCode: ${rawAssessmentId}`,
                ),
                "parseAssessmentIdInCheckSession",
              );
              setErrorMessage("Invalid assessment information.");
              return false;
            }
          }
        }

        assessmentName.value = userData.assessment_name;
        assessmentInfo.value = {
          id: assessmentId.value,
          name: userData.assessment_name,
          is_final: false, // Default to false, will be updated when quiz starts
        };

        // Also set the session code
        sessionCode.value = code;

        // Note: fetchAssessmentInfo will be called centrally in onMounted to avoid duplicates
        // Only call it here if assessmentId is available and we don't already have assessment info
        if (assessmentId.value && !assessmentInfo.value?.duration_minutes) {
          await fetchAssessmentInfo();
        }

        return true;
      } else {
        setErrorMessage("Could not retrieve username for this session code.");
        isSessionCodeValid.value = false;
        username.value = "";
        return false;
      }
    } else {
      setErrorMessage("Invalid session code. Please check and try again.");
      isSessionCodeValid.value = false;
      username.value = "";
      return false;
    }
  } catch (err) {
    isSessionCodeValid.value = false;
    username.value = "";

    const errorInfo = extractErrorInfo(err);
    if (errorInfo.code === 404) {
      setErrorMessage("Invalid session code. Please check and try again.");
    } else {
      setErrorMessage(
        errorInfo.message || "Error validating session code. Please try again.",
      );
    }
    return false;
  }
};

const onSessionCodeChange = () => {
  if (sessionCodeCheckTimeout.value) {
    clearTimeout(sessionCodeCheckTimeout.value);
  }

  isSessionCodeValid.value = false;
  clearMessage();

  if (existingSessionCode.value && existingSessionCode.value.length === 6) {
    debug("Session code entered, validating after delay", {
      code: existingSessionCode.value,
    });
    sessionCodeCheckTimeout.value = setTimeout(() => {
      checkSessionCode(existingSessionCode.value)
        .then((isValid) => {
          debug("Session code validation result", {
            code: existingSessionCode.value,
            isValid: isValid,
            username: username.value,
          });
        })
        .catch((err) => {
          debug("Error validating session code", { error: err });
        });
    }, 500);
  } else {
    username.value = "";
  }
};

// Helper functions for startQuiz
const validateExistingSessionCode = async () => {
  if (!isSessionCodeValid.value) {
    // Try to validate the session code one more time
    debug("Session code not validated yet, validating now", {
      code: existingSessionCode.value,
    });
    const isValid = await checkSessionCode(existingSessionCode.value);
    if (!isValid) {
      setErrorMessage("Please enter a valid session code or username");
      return false;
    }
  }
  return true;
};

const getSessionValidationData = async () => {
  // Only validate if we don't have recent validation data
  if (!isSessionCodeValid.value || !assessmentId.value) {
    try {
      return await validateSessionCodeCached(existingSessionCode.value);
    } catch (error) {
      const errorInfo = extractErrorInfo(error);
      setErrorMessage(
        errorInfo.message || "Error validating session. Please try again.",
      );
      throw error;
    }
  }
  return null;
};

const updateTimeFromSessionData = (sessionData, sessionStatus) => {
  if (
    sessionData.remaining_time_seconds !== undefined &&
    sessionData.remaining_time_seconds !== null
  ) {
    // For pending sessions, the backend returns the full duration which might be larger than our current totalQuizTime
    if (
      sessionStatus === "pending" &&
      sessionData.remaining_time_seconds > totalQuizTime.value
    ) {
      // Update totalQuizTime to match the backend's duration for this assessment
      totalQuizTime.value = sessionData.remaining_time_seconds;
      timeRemaining.value = sessionData.remaining_time_seconds;
    } else {
      // For all other cases, use the helper function with special handling for pending sessions
      const forceUpdate = sessionStatus === "pending";
      if (forceUpdate) {
        // For pending sessions, always update regardless of difference
        if (
          sessionData.remaining_time_seconds >= 0 &&
          sessionData.remaining_time_seconds <= totalQuizTime.value
        ) {
          timeRemaining.value = sessionData.remaining_time_seconds;
        }
      } else {
        // For in-progress sessions, use normal update logic
        updateTimeFromBackend(
          sessionData.remaining_time_seconds,
          "startQuiz",
        );
      }
    }
  }
};

const validateSessionStatus = (sessionStatus) => {
  // Check if session is completed
  if (sessionStatus === "completed") {
    setErrorMessage(
      "This session has already been completed and cannot be used again.",
    );
    return false;
  }

  // Check if session is expired
  if (sessionStatus === "expired") {
    setErrorMessage("This session has expired and cannot be used.");
    return false;
  }

  return true;
};

const handleSessionStatusTransition = async (sessionStatus) => {
  // If session is pending, start it first
  if (sessionStatus === "pending") {
    try {
      const startSessionResponse = await api.quiz.startSession({
        session_code: existingSessionCode.value,
      });

      const startSessionData = extractResponseData(startSessionResponse);
      if (!startSessionData) {
        throw new Error("Failed to start session");
      }

      // Invalidate session validation cache since session status has changed
      invalidateSessionValidationCache();
    } catch (startError) {
      logError(startError, "startSession");
      const errorInfo = extractErrorInfo(startError);
      setErrorMessage(
        errorInfo.message || "Failed to start session. Please try again.",
      );
      throw startError;
    }
  } else if (sessionStatus === "in_progress") {
    // Session is already in progress - we're resuming
    isResuming.value = true;
  } else {
    // Session is not in a valid state
    setErrorMessage(`Session is not available. Status: ${sessionStatus}`);
    throw new Error(`Invalid session status: ${sessionStatus}`);
  }
};

const startExistingSession = async () => {
  if (!(await validateExistingSessionCode())) {
    return false;
  }

  // Double-check session status before starting quiz
  let sessionData = null;
  let sessionStatus = null;

  try {
    sessionData = await getSessionValidationData();
  } catch (error) {
    return false;
  }

  if (sessionData) {
    sessionStatus = sessionData.session_status;

    // Update remaining time from session validation if available
    updateTimeFromSessionData(sessionData, sessionStatus);

    if (!validateSessionStatus(sessionStatus)) {
      return false;
    }

    try {
      await handleSessionStatusTransition(sessionStatus);
    } catch (error) {
      return false;
    }
  }

  // Session code is valid, proceed with the quiz
  sessionCode.value = existingSessionCode.value; // Set the session code
  quizStarted.value = true;
  sessionCode.value = existingSessionCode.value;

  // Initialize fullscreen mode
  await fullscreenQuiz.initializeFullscreenQuiz();

  // Initialize the quiz session and fetch first question
  setTimeout(() => {
    initializeQuizSession();
  }, 500);
  return true;
};

const startQuiz = async () => {
  debug("Starting quiz with form submission", {
    existingSessionCode: existingSessionCode.value,
    username: username.value,
    isSessionCodeValid: isSessionCodeValid.value,
  });

  // If using existing session code, verify it's valid
  if (existingSessionCode.value) {
    const success = await startExistingSession();
    if (success) {
      return;
    } else {
      return;
    }
  }

const validateNewSessionInput = () => {
  if (!username.value.trim()) {
    setErrorMessage("Please enter your username");
    return false;
  }
  return true;
};

const prepareAssessmentId = () => {
  // Ensure we have a valid assessment ID
  let assessmentIdToUse = assessmentId.value;

  // Make sure we have a numeric ID for the API (should already be decoded)
  if (assessmentIdToUse && typeof assessmentIdToUse !== "number") {
    assessmentIdToUse = parseInt(assessmentIdToUse);
  }

  if (!assessmentIdToUse || isNaN(assessmentIdToUse)) {
    setErrorMessage("Invalid assessment ID. Please try again.");
    return null;
  }

  return assessmentIdToUse;
};

const extractSessionCodeFromResponse = async (data) => {
  if (!data || !data.sessions || data.sessions.length === 0) {
    setErrorMessage("Failed to create quiz session. Please try again.");
    return null;
  }

  // Extract session code from the first session
  const sessionData = data.sessions[0];

  // Use the standardized session code extraction
  const extractedCode = extractSessionCode(sessionData);

  if (extractedCode) {
    return extractedCode;
  } else if (sessionData.id_hash) {
    // Try to decode the hash to get the session code
    try {
      const decodedCode = await decodeSessionCodeFromHash(sessionData.id_hash);
      if (decodedCode) {
        return decodedCode;
      } else {
        throw new Error("Failed to decode session hash");
      }
    } catch (err) {
      logError(err, "decodeSessionHashForNewSession");
      setErrorMessage("Failed to create session code. Please try again.");
      return null;
    }
  } else {
    setErrorMessage("Invalid session data received. Please try again.");
    return null;
  }
};

const createNewSession = async () => {
  if (!validateNewSessionInput()) {
    return false;
  }

  const assessmentIdToUse = prepareAssessmentId();
  if (!assessmentIdToUse) {
    return false;
  }

  isLoading.value = true;
  clearMessage();

  try {
    const response = await api.admin.createSession({
      assessment_id: assessmentIdToUse,
      usernames: username.value.trim(),
    });
    logApiResponse(
      "POST",
      "/api/admin/sessions",
      response?.status || 200,
      response?.data,
    );

    const data = extractResponseData(response);
    const extractedCode = await extractSessionCodeFromResponse(data);

    if (!extractedCode) {
      return false;
    }

    sessionCode.value = extractedCode;
    quizStarted.value = true;

    // Initialize fullscreen mode
    await fullscreenQuiz.initializeFullscreenQuiz();

    // Initialize the quiz session and fetch first question
    setTimeout(() => {
      initializeQuizSession();
    }, 500);

    return true;
  } catch (err) {
    const errorInfo = extractErrorInfo(err);
    setErrorMessage(
      errorInfo.message || "Failed to start quiz. Please try again.",
    );
    return false;
  } finally {
    isLoading.value = false;
  }
};

  // Otherwise, create a new session with the provided username
  await createNewSession();
};

// Process route params from URL
const processRouteParams = async () => {
  // Check if this is a direct start from UserSessions page
  isDirectStart.value = route.query.directStart === "true";
  debug("processRouteParams", {
    isDirectStart: isDirectStart.value,
    query: route.query,
  });

  // Check if we have a session code in the route
  if (route.params.sessionCode) {
    const routeSessionCode = route.params.sessionCode;

    // Check if it's a hash ID (contains letters)
    isRouteHashId.value = isHashId(routeSessionCode);

    if (isRouteHashId.value) {
      try {
        // Decode the hash to get the session code
        const decodedCode = await decodeSessionCodeFromHash(routeSessionCode);
        if (decodedCode) {
          // Set the decoded session code
          existingSessionCode.value = decodedCode;
          sessionCode.value = decodedCode;

          // Validate the session code and get assessment ID
          const isValid = await checkSessionCode(decodedCode);

          // If validation failed, clear the session code
          if (!isValid) {
            sessionCode.value = "";
            existingSessionCode.value = "";
          }
        } else {
          setErrorMessage("Invalid session code format.");
        }
      } catch (err) {
        logError(err, "decodeSessionHash");
        setErrorMessage("Failed to decode session code.");
      }
    } else {
      // It's a regular session code
      existingSessionCode.value = routeSessionCode;

      // Validate the session code and get assessment ID
      const isValid = await checkSessionCode(routeSessionCode);

      // If validation failed, clear the session code
      if (!isValid) {
        sessionCode.value = "";
        existingSessionCode.value = "";
      }
    }
  }

  // Check if we have an assessment ID in the route (for direct assessment access)
  if (route.params.assessmentId) {
    const routeAssessmentId = route.params.assessmentId;

    // Check if it's a hash ID
    if (isHashId(routeAssessmentId)) {
      try {
        // Decode the assessment hash to get the real assessment ID
        const decodedId = await decodeAssessmentId(routeAssessmentId);
        if (decodedId) {
          assessmentId.value = decodedId;
        } else {
          setErrorMessage("Invalid assessment link.");
          return;
        }
      } catch (err) {
        logError(err, "decodeAssessmentHash");
        setErrorMessage("Failed to decode assessment link.");
        return;
      }
    } else {
      // It's a regular ID
      const parsedId = parseInt(routeAssessmentId);
      if (!isNaN(parsedId) && parsedId > 0) {
        assessmentId.value = parsedId;
      } else {
        setErrorMessage("Invalid assessment ID in URL.");
        return;
      }
    }
  }
};

onMounted(async () => {
  // Disable scrolling
  document.body.style.overflow = "hidden";
  document.body.style.height = "100vh";
  document.body.style.position = "fixed";
  document.body.style.width = "100%";
  document.documentElement.style.overflow = "hidden";

  setupPageVisibilityListeners();

  try {
    // Check for auto-fill data in localStorage
    const autoFillData = localStorage.getItem("quiz_auto_fill");
    let autoFillInfo = null;
    let hasAutoFillData = false;

    if (autoFillData) {
      try {
        autoFillInfo = JSON.parse(autoFillData);
        debug("Found auto-fill data", { autoFillInfo });

        // Check if the data is recent (within the last 5 minutes)
        const isRecent =
          autoFillInfo.timestamp &&
          new Date().getTime() - autoFillInfo.timestamp < 5 * 60 * 1000;

        if (isRecent || !autoFillInfo.timestamp) {
          hasAutoFillData = true;

          // Auto-fill form fields if data exists
          if (autoFillInfo) {
            if (autoFillInfo.username) {
              username.value = autoFillInfo.username;
              debug("Auto-filled username", { username: username.value });
            }

            if (autoFillInfo.email) {
              email.value = autoFillInfo.email;
              debug("Auto-filled email", { email: email.value });
            }

            if (autoFillInfo.session_code) {
              existingSessionCode.value = autoFillInfo.session_code;
              // Also set sessionCode to ensure it's available for direct start
              sessionCode.value = autoFillInfo.session_code;
              debug("Auto-filled session code", {
                sessionCode: sessionCode.value,
              });
            }

            if (autoFillInfo.assessment_name) {
              assessmentName.value = autoFillInfo.assessment_name;
              debug("Auto-filled assessment name", {
                assessmentName: assessmentName.value,
              });
            }

            // If auto_start is true, set isDirectStart to true
            if (autoFillInfo.auto_start === true) {
              isDirectStart.value = true;
              debug("Auto-start flag set to true");
            }
          }
        } else {
          debug("Auto-fill data is stale, ignoring", {
            timestamp: autoFillInfo.timestamp,
            currentTime: new Date().getTime(),
            age:
              (new Date().getTime() - autoFillInfo.timestamp) / 1000 +
              " seconds",
          });
        }

        // Clear the auto-fill data to prevent it from being used again
        localStorage.removeItem("quiz_auto_fill");
      } catch (parseError) {
        logError(parseError, "parseAutoFillData");
      }
    } else {
      debug("No auto-fill data found in localStorage");
    }

    await processRouteParams();

    if (assessmentId.value) {
      await fetchAssessmentInfo();
    }

    // Check if autoFill parameter is in the URL
    const isAutoFill = route.query.autoFill === "true";

    // If direct start is requested and we have a session code
    if (
      sessionCode.value &&
      (isDirectStart.value ||
        isAutoFill ||
        (hasAutoFillData && autoFillInfo?.auto_start === true))
    ) {
      debug("Attempting direct start with session code", {
        sessionCode: sessionCode.value,
        username: username.value,
        isDirectStart: isDirectStart.value,
        isAutoFill: isAutoFill,
        hasAutoFillData: hasAutoFillData,
      });

      try {
        // Validate the session code first
        const isValid = await checkSessionCode(sessionCode.value);

        if (isValid) {
          // Direct start from UserSessions - skip the form and start quiz immediately
          quizStarted.value = true;
          debug("Auto-starting quiz with session code", {
            sessionCode: sessionCode.value,
            username: username.value,
          });

          // Initialize fullscreen mode
          await fullscreenQuiz.initializeFullscreenQuiz();

          // Use a shorter timeout to make it feel more responsive
          setTimeout(() => {
            initializeQuizSession();
          }, 300);
        } else {
          // Session code validation failed, show the form with error message
          debug("Session code validation failed for direct start", {
            sessionCode: sessionCode.value,
          });
          setErrorMessage("Invalid session code. Please check and try again.");
        }
      } catch (err) {
        debug("Error during direct start", { error: err });
        setErrorMessage("Error starting session. Please try again.");
      }
    } else if (sessionCode.value) {
      // Regular flow - user needs to fill form and click start
      // Don't auto-start the quiz, just pre-fill the session code
      // The existingSessionCode will be pre-filled in the form
      debug("Pre-filling session code without auto-start", {
        sessionCode: sessionCode.value,
      });

      // Validate the session code to auto-fill username
      checkSessionCode(sessionCode.value).catch((err) => {
        debug("Error validating pre-filled session code", { error: err });
      });
    } else if (!assessmentId.value) {
      setErrorMessage("Invalid assessment link or session code.");
    }
  } catch (err) {
    logError(err, "onMounted");
    setErrorMessage("Failed to initialize quiz. Please try again.");
  }
});

onBeforeRouteLeave((to, from, next) => {
  if (quizStarted.value && !quizCompleted.value && timeRemaining.value > 0) {
    const confirmed = confirmQuizQuit({
      questionsAttempted: questionsAttempted.value,
    });

    if (confirmed) {
      submitQuizOnQuit();
      logQuizQuit("route_leave", {
        questionsAttempted: questionsAttempted.value,
        correctAnswers: correctAnswers.value,
        finalScore: currentScore.value,
        sessionCode: sessionCode.value,
      });
      next();
    } else {
      next(false);
    }
  } else {
    next();
  }
});

onUnmounted(async () => {
  if (quizStarted.value && !quizCompleted.value && timeRemaining.value > 0) {
    submitQuizOnQuit();
    logQuizQuit("component_unmount", {
      questionsAttempted: questionsAttempted.value,
      correctAnswers: correctAnswers.value,
      finalScore: currentScore.value,
      sessionCode: sessionCode.value,
    });
  }

  // Cleanup fullscreen mode
  await fullscreenQuiz.cleanupFullscreenQuiz();

  detailedResultsModal.cleanup();
  removePageVisibilityListeners();

  if (sessionCodeCheckTimeout.value) {
    clearTimeout(sessionCodeCheckTimeout.value);
  }

  document.body.style.overflow = "";
  document.body.style.height = "";
  document.body.style.position = "";
  document.body.style.width = "";
  document.documentElement.style.overflow = "";
});
</script>

<style scoped>
/* Fullscreen quiz styles */
:fullscreen {
  background: #0a0e17 !important;
}

:-webkit-full-screen {
  background: #0a0e17 !important;
}

:-moz-full-screen {
  background: #0a0e17 !important;
}

:-ms-fullscreen {
  background: #0a0e17 !important;
}

/* Hide scrollbars in fullscreen */
:fullscreen ::-webkit-scrollbar {
  display: none;
}

:-webkit-full-screen ::-webkit-scrollbar {
  display: none;
}

/* Prevent text selection during quiz */
.quiz-content {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection for questions and answers */
.quiz-content .question-text,
.quiz-content .answer-option {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Fullscreen quit modal styles */
.fullscreen-quit-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 999999 !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(8px) !important;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Ensure modal content is centered and visible */
.fullscreen-quit-modal .modal-content {
  animation: scaleIn 0.3s ease-in-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
