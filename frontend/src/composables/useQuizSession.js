import { ref, computed } from 'vue'
import { useApiRequest } from './useApiRequest'
import { useNotification } from './useNotification'
import { useFullscreenQuiz } from './useFullscreenQuiz'
import { extractResponseData } from '@/utils/responseUtils'
import { logError, logApiRequest, logApiResponse, debug, warning } from '@/utils/logging'
import { getErrorMessage } from '@/utils/errorHandling'

export function useQuizSession() {
  const { api } = useApiRequest()
  const { setErrorMessage, showSuccess } = useNotification()
  const fullscreenQuiz = useFullscreenQuiz()

  // Session state
  const sessionCode = ref('')
  const username = ref('')
  const assessmentId = ref(null)
  const assessmentInfo = ref(null)
  const assessmentName = ref('')
  const isDynamicAssessment = ref(true)
  const quizStarted = ref(false)
  const isLoading = ref(false)

  // Quiz state
  const currentQuestion = ref(null)
  const currentQuestionIndex = ref(0)
  const selectedAnswer = ref('')
  const answerSubmitted = ref(false)
  const questionsAttempted = ref(0)
  const correctAnswers = ref(0)
  const currentScore = ref(0)
  const maxQuestions = ref(20)
  const quizCompleted = ref(false)

  // Computed properties
  const isFormValid = computed(() => {
    return username.value.trim() && sessionCode.value.trim()
  })

  const progressPercentage = computed(() => {
    if (maxQuestions.value === 0) return 0
    return Math.round((questionsAttempted.value / maxQuestions.value) * 100)
  })

  // Fetch assessment info
  const fetchAssessmentInfo = async (id) => {
    try {
      const response = await api.admin.getAssessmentById(id)
      const data = extractResponseData(response)

      if (data) {
        assessmentInfo.value = data
        assessmentName.value = data.name
        maxQuestions.value = data.total_questions
        isDynamicAssessment.value = data.question_selection_mode === 'dynamic'

        debug('Assessment info loaded', {
          name: data.name,
          totalQuestions: data.total_questions,
          mode: data.question_selection_mode
        })
      }
    } catch (error) {
      logError(error, 'fetchAssessmentInfo')
      setErrorMessage(getErrorMessage(error, 'Failed to load assessment information'))
    }
  }

  // Validate session code
  const validateSessionCode = async (code) => {
    try {
      const response = await api.admin.validateSessionCode(code)
      const data = extractResponseData(response)

      if (data && data.valid) {
        if (data.username) {
          username.value = data.username
        }
        return true
      } else {
        setErrorMessage('Invalid or expired session code')
        return false
      }
    } catch (error) {
      logError(error, 'validateSessionCode')
      setErrorMessage(getErrorMessage(error, 'Failed to validate session code'))
      return false
    }
  }

  // Create new session
  const createSession = async () => {
    if (!assessmentId.value || !username.value.trim()) {
      setErrorMessage('Missing required information')
      return false
    }

    try {
      isLoading.value = true
      const response = await api.admin.createSession({
        assessment_id: assessmentId.value,
        usernames: username.value.trim()
      })

      logApiResponse('POST', '/api/admin/sessions', response?.status || 200, response?.data)

      const data = extractResponseData(response)
      const extractedCode = await extractSessionCodeFromResponse(data)

      if (!extractedCode) {
        return false
      }

      sessionCode.value = extractedCode
      return true
    } catch (error) {
      logError(error, 'createSession')
      setErrorMessage(getErrorMessage(error, 'Failed to create session'))
      return false
    } finally {
      isLoading.value = false
    }
  }

  // Extract session code from response
  const extractSessionCodeFromResponse = async (data) => {
    try {
      if (data?.session_code) {
        return data.session_code
      }

      if (data?.sessions && Array.isArray(data.sessions) && data.sessions.length > 0) {
        const firstSession = data.sessions[0]
        if (firstSession?.session_code) {
          return firstSession.session_code
        }
      }

      setErrorMessage('No session code found in response')
      return null
    } catch (error) {
      logError(error, 'extractSessionCodeFromResponse')
      setErrorMessage('Failed to extract session code')
      return null
    }
  }

  // Start quiz session
  const startQuizSession = async () => {
    if (!isFormValid.value) {
      setErrorMessage('Please fill in all required fields')
      return false
    }

    try {
      isLoading.value = true

      // Validate session code first
      const isValid = await validateSessionCode(sessionCode.value)
      if (!isValid) {
        return false
      }

      quizStarted.value = true

      // Initialize fullscreen mode
      await fullscreenQuiz.initializeFullscreenQuiz()

      // Initialize quiz session
      setTimeout(() => {
        initializeQuizSession()
      }, 500)

      return true
    } catch (error) {
      logError(error, 'startQuizSession')
      setErrorMessage(getErrorMessage(error, 'Failed to start quiz session'))
      return false
    } finally {
      isLoading.value = false
    }
  }

  // Initialize quiz session (placeholder for actual implementation)
  const initializeQuizSession = () => {
    debug('Quiz session initialized', {
      sessionCode: sessionCode.value,
      username: username.value,
      assessmentId: assessmentId.value
    })
  }

  // Reset session
  const resetSession = () => {
    sessionCode.value = ''
    username.value = ''
    assessmentId.value = null
    assessmentInfo.value = null
    assessmentName.value = ''
    quizStarted.value = false
    currentQuestion.value = null
    currentQuestionIndex.value = 0
    selectedAnswer.value = ''
    answerSubmitted.value = false
    questionsAttempted.value = 0
    correctAnswers.value = 0
    currentScore.value = 0
    quizCompleted.value = false
  }

  return {
    // State
    sessionCode,
    username,
    assessmentId,
    assessmentInfo,
    assessmentName,
    isDynamicAssessment,
    quizStarted,
    isLoading,
    currentQuestion,
    currentQuestionIndex,
    selectedAnswer,
    answerSubmitted,
    questionsAttempted,
    correctAnswers,
    currentScore,
    maxQuestions,
    quizCompleted,
    
    // Computed
    isFormValid,
    progressPercentage,
    
    // Methods
    fetchAssessmentInfo,
    validateSessionCode,
    createSession,
    startQuizSession,
    resetSession
  }
}
